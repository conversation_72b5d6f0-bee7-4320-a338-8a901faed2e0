{"voices": [{"voice_id": "21m00Tcm4TlvDq8ikWAM", "name": "<PERSON>", "samples": null, "category": "premade", "fine_tuning": {"is_allowed_to_fine_tune": false, "state": {}, "verification_failures": [], "verification_attempts_count": 0, "manual_verification_requested": false, "language": null, "progress": {}, "message": {}, "dataset_duration_seconds": null, "verification_attempts": null, "slice_ids": null, "manual_verification": null, "max_verification_attempts": null, "next_max_verification_attempts_reset_unix_ms": null}, "labels": {"accent": "american", "description": "calm", "age": "young", "gender": "female", "use_case": "narration"}, "description": null, "preview_url": "https://storage.googleapis.com/eleven-public-prod/premade/voices/21m00Tcm4TlvDq8ikWAM/b4928a68-c03b-411f-8533-3d5c299fd451.mp3", "available_for_tiers": [], "settings": null, "sharing": null, "high_quality_base_model_ids": [], "verified_languages": [], "safety_control": null, "voice_verification": {"requires_verification": false, "is_verified": false, "verification_failures": [], "verification_attempts_count": 0, "language": null, "verification_attempts": null}, "permission_on_resource": null, "is_owner": false, "is_legacy": true, "is_mixed": false, "created_at_unix": null}, {"voice_id": "29vD33N1CtxCmqQRPOHJ", "name": "<PERSON>", "samples": null, "category": "premade", "fine_tuning": {"is_allowed_to_fine_tune": true, "state": {"eleven_multilingual_v2": "fine_tuned", "eleven_turbo_v2_5": "fine_tuned", "eleven_flash_v2_5": "fine_tuned", "eleven_v2_flash": "fine_tuned", "eleven_v2_5_flash": "fine_tuned", "eleven_turbo_v2": "fine_tuned", "eleven_flash_v2": "fine_tuned"}, "verification_failures": [], "verification_attempts_count": 0, "manual_verification_requested": false, "language": "en", "progress": {"eleven_flash_v2_5": 1, "eleven_v2_flash": 1, "eleven_flash_v2": 1, "eleven_v2_5_flash": 1}, "message": {"eleven_multilingual_v2": "", "eleven_turbo_v2_5": "", "eleven_flash_v2_5": "Done!", "eleven_v2_flash": "Done!", "eleven_v2_5_flash": "Done!", "eleven_turbo_v2": "", "eleven_flash_v2": "Done!"}, "dataset_duration_seconds": null, "verification_attempts": null, "slice_ids": null, "manual_verification": null, "max_verification_attempts": 5, "next_max_verification_attempts_reset_unix_ms": 1700000000000}, "labels": {"accent": "american", "description": "well-rounded", "age": "middle_aged", "gender": "male", "use_case": "news"}, "description": null, "preview_url": "https://storage.googleapis.com/eleven-public-prod/premade/voices/29vD33N1CtxCmqQRPOHJ/b99fc51d-12d3-4312-b480-a8a45a7d51ef.mp3", "available_for_tiers": [], "settings": null, "sharing": null, "high_quality_base_model_ids": ["eleven_v2_flash", "eleven_flash_v2", "eleven_turbo_v2_5", "eleven_multilingual_v2", "eleven_v2_5_flash", "eleven_flash_v2_5", "eleven_turbo_v2"], "verified_languages": [], "safety_control": null, "voice_verification": {"requires_verification": false, "is_verified": false, "verification_failures": [], "verification_attempts_count": 0, "language": null, "verification_attempts": null}, "permission_on_resource": null, "is_owner": false, "is_legacy": true, "is_mixed": false, "created_at_unix": null}, {"voice_id": "2EiwWnXFnvU5JabPnv8n", "name": "Clyde", "samples": null, "category": "premade", "fine_tuning": {"is_allowed_to_fine_tune": true, "state": {"eleven_flash_v2_5": "fine_tuned", "eleven_turbo_v2": "fine_tuned", "eleven_flash_v2": "fine_tuned", "eleven_v2_flash": "fine_tuned", "eleven_v2_5_flash": "fine_tuned"}, "verification_failures": [], "verification_attempts_count": 0, "manual_verification_requested": false, "language": "en", "progress": {"eleven_flash_v2_5": 1, "eleven_v2_flash": 1, "eleven_flash_v2": 1, "eleven_v2_5_flash": 1}, "message": {"eleven_multilingual_v2": "", "eleven_turbo_v2_5": "", "eleven_flash_v2_5": "Done!", "eleven_v2_flash": "Done!", "eleven_v2_5_flash": "Done!", "eleven_turbo_v2": "", "eleven_flash_v2": "Done!"}, "dataset_duration_seconds": null, "verification_attempts": null, "slice_ids": null, "manual_verification": null, "max_verification_attempts": 5, "next_max_verification_attempts_reset_unix_ms": 1700000000000}, "labels": {"accent": "american", "description": "war veteran", "age": "middle_aged", "gender": "male", "use_case": "characters"}, "description": null, "preview_url": "https://storage.googleapis.com/eleven-public-prod/premade/voices/2EiwWnXFnvU5JabPnv8n/65d80f52-703f-4cae-a91d-75d4e200ed02.mp3", "available_for_tiers": [], "settings": null, "sharing": null, "high_quality_base_model_ids": ["eleven_v2_flash", "eleven_flash_v2", "eleven_turbo_v2_5", "eleven_multilingual_v2", "eleven_multilingual_v1", "eleven_v2_5_flash", "eleven_flash_v2_5", "eleven_turbo_v2"], "verified_languages": [], "safety_control": null, "voice_verification": {"requires_verification": false, "is_verified": false, "verification_failures": [], "verification_attempts_count": 0, "language": null, "verification_attempts": null}, "permission_on_resource": null, "is_owner": false, "is_legacy": true, "is_mixed": false, "created_at_unix": null}, {"voice_id": "5Q0t7uMcjvnagumLfvZi", "name": "<PERSON>", "samples": null, "category": "premade", "fine_tuning": {"is_allowed_to_fine_tune": true, "state": {"eleven_multilingual_v2": "fine_tuned", "eleven_turbo_v2_5": "fine_tuned", "eleven_flash_v2_5": "fine_tuned", "eleven_v2_flash": "fine_tuned", "eleven_v2_5_flash": "fine_tuned", "eleven_turbo_v2": "fine_tuned", "eleven_flash_v2": "fine_tuned"}, "verification_failures": [], "verification_attempts_count": 0, "manual_verification_requested": false, "language": "en", "progress": {"eleven_flash_v2_5": 1, "eleven_v2_flash": 1, "eleven_flash_v2": 1, "eleven_v2_5_flash": 1}, "message": {"eleven_multilingual_v2": "", "eleven_turbo_v2_5": "", "eleven_flash_v2_5": "Done!", "eleven_v2_flash": "Done!", "eleven_v2_5_flash": "Done!", "eleven_turbo_v2": "", "eleven_flash_v2": "Done!"}, "dataset_duration_seconds": null, "verification_attempts": null, "slice_ids": null, "manual_verification": null, "max_verification_attempts": 5, "next_max_verification_attempts_reset_unix_ms": 1700000000000}, "labels": {"accent": "american", "description": "authoritative", "age": "middle_aged", "gender": "male", "use_case": "news"}, "description": null, "preview_url": "https://storage.googleapis.com/eleven-public-prod/premade/voices/5Q0t7uMcjvnagumLfvZi/a4aaa30e-54c4-44a4-8e46-b9b00505d963.mp3", "available_for_tiers": [], "settings": null, "sharing": null, "high_quality_base_model_ids": ["eleven_v2_flash", "eleven_flash_v2", "eleven_turbo_v2_5", "eleven_multilingual_v2", "eleven_v2_5_flash", "eleven_flash_v2_5", "eleven_turbo_v2"], "verified_languages": [], "safety_control": null, "voice_verification": {"requires_verification": false, "is_verified": false, "verification_failures": [], "verification_attempts_count": 0, "language": null, "verification_attempts": null}, "permission_on_resource": null, "is_owner": false, "is_legacy": true, "is_mixed": false, "created_at_unix": null}, {"voice_id": "9BWtsMINqrJLrRacOk9x", "name": "Aria", "samples": null, "category": "premade", "fine_tuning": {"is_allowed_to_fine_tune": true, "state": {"eleven_multilingual_v2": "fine_tuned", "eleven_turbo_v2_5": "fine_tuned", "eleven_flash_v2_5": "fine_tuned", "eleven_v2_flash": "fine_tuned", "eleven_v2_5_flash": "fine_tuned", "eleven_turbo_v2": "fine_tuned", "eleven_flash_v2": "fine_tuned"}, "verification_failures": [], "verification_attempts_count": 0, "manual_verification_requested": false, "language": "en", "progress": {"eleven_flash_v2_5": 1, "eleven_v2_flash": 1, "eleven_flash_v2": 1, "eleven_v2_5_flash": 1}, "message": {"eleven_flash_v2_5": "Done!", "eleven_v2_flash": "Done!", "eleven_flash_v2": "Done!", "eleven_v2_5_flash": "Done!"}, "dataset_duration_seconds": null, "verification_attempts": null, "slice_ids": null, "manual_verification": null, "max_verification_attempts": 5, "next_max_verification_attempts_reset_unix_ms": 1700000000000}, "labels": {"accent": "american", "descriptive": "husky", "age": "middle_aged", "gender": "female", "language": "en", "use_case": "informative_educational"}, "description": "A middle-aged female with an African-American accent. Calm with a hint of rasp.", "preview_url": "https://storage.googleapis.com/eleven-public-prod/premade/voices/9BWtsMINqrJLrRacOk9x/405766b8-1f4e-4d3c-aba1-6f25333823ec.mp3", "available_for_tiers": [], "settings": null, "sharing": null, "high_quality_base_model_ids": ["eleven_v2_flash", "eleven_flash_v2", "eleven_turbo_v2_5", "eleven_multilingual_v2", "eleven_v2_5_flash", "eleven_flash_v2_5", "eleven_turbo_v2"], "verified_languages": [{"language": "en", "model_id": "eleven_v2_flash", "accent": "american", "locale": "en-US", "preview_url": "https://storage.googleapis.com/eleven-public-prod/premade/voices/9BWtsMINqrJLrRacOk9x/405766b8-1f4e-4d3c-aba1-6f25333823ec.mp3"}, {"language": "en", "model_id": "eleven_flash_v2", "accent": "american", "locale": "en-US", "preview_url": "https://storage.googleapis.com/eleven-public-prod/premade/voices/9BWtsMINqrJLrRacOk9x/405766b8-1f4e-4d3c-aba1-6f25333823ec.mp3"}, {"language": "en", "model_id": "eleven_turbo_v2_5", "accent": "american", "locale": "en-US", "preview_url": "https://storage.googleapis.com/eleven-public-prod/premade/voices/9BWtsMINqrJLrRacOk9x/405766b8-1f4e-4d3c-aba1-6f25333823ec.mp3"}, {"language": "en", "model_id": "eleven_multilingual_v2", "accent": "american", "locale": "en-US", "preview_url": "https://storage.googleapis.com/eleven-public-prod/premade/voices/9BWtsMINqrJLrRacOk9x/405766b8-1f4e-4d3c-aba1-6f25333823ec.mp3"}, {"language": "en", "model_id": "eleven_v2_5_flash", "accent": "american", "locale": "en-US", "preview_url": "https://storage.googleapis.com/eleven-public-prod/premade/voices/9BWtsMINqrJLrRacOk9x/405766b8-1f4e-4d3c-aba1-6f25333823ec.mp3"}, {"language": "en", "model_id": "eleven_flash_v2_5", "accent": "american", "locale": "en-US", "preview_url": "https://storage.googleapis.com/eleven-public-prod/premade/voices/9BWtsMINqrJLrRacOk9x/405766b8-1f4e-4d3c-aba1-6f25333823ec.mp3"}, {"language": "en", "model_id": "eleven_turbo_v2", "accent": "american", "locale": "en-US", "preview_url": "https://storage.googleapis.com/eleven-public-prod/premade/voices/9BWtsMINqrJLrRacOk9x/405766b8-1f4e-4d3c-aba1-6f25333823ec.mp3"}, {"language": "fr", "model_id": "eleven_multilingual_v2", "accent": "standard", "locale": "fr-FR", "preview_url": "https://storage.googleapis.com/eleven-public-prod/premade/voices/9BWtsMINqrJLrRacOk9x/ae97c224-d4d0-4e03-a9ab-36f031f48e94.mp3"}, {"language": "zh", "model_id": "eleven_multilingual_v2", "accent": "standard", "locale": "cmn-CN", "preview_url": "https://storage.googleapis.com/eleven-public-prod/premade/voices/9BWtsMINqrJLrRacOk9x/b6a58993-1cf7-4ea8-b3b1-a60b3641d5bf.mp3"}, {"language": "tr", "model_id": "eleven_multilingual_v2", "accent": "standard", "locale": "tr-TR", "preview_url": "https://storage.googleapis.com/eleven-public-prod/premade/voices/9BWtsMINqrJLrRacOk9x/9342915e-dd15-4a11-af37-96670decd65a.mp3"}], "safety_control": null, "voice_verification": {"requires_verification": false, "is_verified": false, "verification_failures": [], "verification_attempts_count": 0, "language": null, "verification_attempts": null}, "permission_on_resource": null, "is_owner": false, "is_legacy": false, "is_mixed": false, "created_at_unix": null}, {"voice_id": "AZnzlk1XvdvUeBnXmlld", "name": "<PERSON><PERSON>", "samples": null, "category": "premade", "fine_tuning": {"is_allowed_to_fine_tune": false, "state": {}, "verification_failures": [], "verification_attempts_count": 0, "manual_verification_requested": false, "language": null, "progress": {}, "message": {}, "dataset_duration_seconds": null, "verification_attempts": null, "slice_ids": null, "manual_verification": null, "max_verification_attempts": null, "next_max_verification_attempts_reset_unix_ms": null}, "labels": {"accent": "american", "description": "strong", "age": "young", "gender": "female", "use_case": "narration"}, "description": null, "preview_url": "https://storage.googleapis.com/eleven-public-prod/premade/voices/AZnzlk1XvdvUeBnXmlld/b3c36b01-f80d-4b16-a698-f83682dee84c.mp3", "available_for_tiers": [], "settings": null, "sharing": null, "high_quality_base_model_ids": [], "verified_languages": [], "safety_control": null, "voice_verification": {"requires_verification": false, "is_verified": false, "verification_failures": [], "verification_attempts_count": 0, "language": null, "verification_attempts": null}, "permission_on_resource": null, "is_owner": false, "is_legacy": true, "is_mixed": false, "created_at_unix": null}, {"voice_id": "CYw3kZ02Hs0563khs1Fj", "name": "<PERSON>", "samples": null, "category": "premade", "fine_tuning": {"is_allowed_to_fine_tune": true, "state": {"eleven_flash_v2_5": "fine_tuned", "eleven_turbo_v2": "fine_tuned", "eleven_flash_v2": "fine_tuned", "eleven_v2_flash": "fine_tuned", "eleven_v2_5_flash": "fine_tuned"}, "verification_failures": [], "verification_attempts_count": 0, "manual_verification_requested": false, "language": "en", "progress": {"eleven_flash_v2_5": 1, "eleven_v2_flash": 1, "eleven_flash_v2": 1, "eleven_v2_5_flash": 1}, "message": {"eleven_multilingual_v2": "", "eleven_turbo_v2_5": "", "eleven_flash_v2_5": "Done!", "eleven_v2_flash": "Done!", "eleven_v2_5_flash": "Done!", "eleven_turbo_v2": "", "eleven_flash_v2": "Done!"}, "dataset_duration_seconds": null, "verification_attempts": null, "slice_ids": null, "manual_verification": null, "max_verification_attempts": 5, "next_max_verification_attempts_reset_unix_ms": 1700000000000}, "labels": {"accent": "british", "description": "conversational", "age": "young", "gender": "male", "use_case": "characters"}, "description": null, "preview_url": "https://storage.googleapis.com/eleven-public-prod/premade/voices/CYw3kZ02Hs0563khs1Fj/872cb056-45d3-419e-b5c6-de2b387a93a0.mp3", "available_for_tiers": [], "settings": null, "sharing": null, "high_quality_base_model_ids": ["eleven_v2_flash", "eleven_flash_v2", "eleven_turbo_v2_5", "eleven_multilingual_v2", "eleven_multilingual_v1", "eleven_v2_5_flash", "eleven_flash_v2_5", "eleven_turbo_v2"], "verified_languages": [], "safety_control": null, "voice_verification": {"requires_verification": false, "is_verified": false, "verification_failures": [], "verification_attempts_count": 0, "language": null, "verification_attempts": null}, "permission_on_resource": null, "is_owner": false, "is_legacy": true, "is_mixed": false, "created_at_unix": null}, {"voice_id": "CwhRBWXzGAHq8TQ4Fs17", "name": "<PERSON>", "samples": null, "category": "premade", "fine_tuning": {"is_allowed_to_fine_tune": true, "state": {"eleven_multilingual_v2": "fine_tuned", "eleven_turbo_v2_5": "failed", "eleven_flash_v2_5": "fine_tuned", "eleven_v2_flash": "fine_tuned", "eleven_v2_5_flash": "fine_tuned", "eleven_turbo_v2": "fine_tuned", "eleven_flash_v2": "fine_tuned"}, "verification_failures": [], "verification_attempts_count": 0, "manual_verification_requested": false, "language": "en", "progress": {"eleven_flash_v2_5": 1, "eleven_v2_flash": 1, "eleven_flash_v2": 1, "eleven_v2_5_flash": 1}, "message": {"eleven_flash_v2_5": "Done!", "eleven_v2_flash": "Done!", "eleven_flash_v2": "Done!", "eleven_v2_5_flash": "Done!"}, "dataset_duration_seconds": null, "verification_attempts": null, "slice_ids": null, "manual_verification": null, "max_verification_attempts": 5, "next_max_verification_attempts_reset_unix_ms": 1700000000000}, "labels": {"accent": "", "description": "confident", "age": "middle_aged", "gender": "male", "language": "en", "use_case": "social media"}, "description": "", "preview_url": "https://storage.googleapis.com/eleven-public-prod/premade/voices/CwhRBWXzGAHq8TQ4Fs17/58ee3ff5-f6f2-4628-93b8-e38eb31806b0.mp3", "available_for_tiers": [], "settings": null, "sharing": null, "high_quality_base_model_ids": ["eleven_v2_flash", "eleven_flash_v2", "eleven_turbo_v2_5", "eleven_multilingual_v2", "eleven_v2_5_flash", "eleven_flash_v2_5", "eleven_turbo_v2"], "verified_languages": [{"language": "en", "model_id": "eleven_v2_flash", "accent": null, "locale": "en-US", "preview_url": "https://storage.googleapis.com/eleven-public-prod/premade/voices/CwhRBWXzGAHq8TQ4Fs17/58ee3ff5-f6f2-4628-93b8-e38eb31806b0.mp3"}, {"language": "en", "model_id": "eleven_flash_v2", "accent": null, "locale": "en-US", "preview_url": "https://storage.googleapis.com/eleven-public-prod/premade/voices/CwhRBWXzGAHq8TQ4Fs17/58ee3ff5-f6f2-4628-93b8-e38eb31806b0.mp3"}, {"language": "en", "model_id": "eleven_turbo_v2_5", "accent": null, "locale": "en-US", "preview_url": "https://storage.googleapis.com/eleven-public-prod/premade/voices/CwhRBWXzGAHq8TQ4Fs17/58ee3ff5-f6f2-4628-93b8-e38eb31806b0.mp3"}, {"language": "en", "model_id": "eleven_multilingual_v2", "accent": null, "locale": "en-US", "preview_url": "https://storage.googleapis.com/eleven-public-prod/premade/voices/CwhRBWXzGAHq8TQ4Fs17/58ee3ff5-f6f2-4628-93b8-e38eb31806b0.mp3"}, {"language": "en", "model_id": "eleven_v2_5_flash", "accent": null, "locale": "en-US", "preview_url": "https://storage.googleapis.com/eleven-public-prod/premade/voices/CwhRBWXzGAHq8TQ4Fs17/58ee3ff5-f6f2-4628-93b8-e38eb31806b0.mp3"}, {"language": "en", "model_id": "eleven_flash_v2_5", "accent": null, "locale": "en-US", "preview_url": "https://storage.googleapis.com/eleven-public-prod/premade/voices/CwhRBWXzGAHq8TQ4Fs17/58ee3ff5-f6f2-4628-93b8-e38eb31806b0.mp3"}, {"language": "en", "model_id": "eleven_turbo_v2", "accent": null, "locale": "en-US", "preview_url": "https://storage.googleapis.com/eleven-public-prod/premade/voices/CwhRBWXzGAHq8TQ4Fs17/58ee3ff5-f6f2-4628-93b8-e38eb31806b0.mp3"}, {"language": "fr", "model_id": "eleven_multilingual_v2", "accent": "standard", "locale": "fr-FR", "preview_url": "https://storage.googleapis.com/eleven-public-prod/premade/voices/CwhRBWXzGAHq8TQ4Fs17/042d9b70-5927-4630-985e-e95107b74ec2.mp3"}, {"language": "de", "model_id": "eleven_multilingual_v2", "accent": "standard", "locale": "de-DE", "preview_url": "https://storage.googleapis.com/eleven-public-prod/premade/voices/CwhRBWXzGAHq8TQ4Fs17/fa6a7658-18a9-4634-a96f-95dc3c47629d.mp3"}, {"language": "nl", "model_id": "eleven_multilingual_v2", "accent": "standard", "locale": "nl-NL", "preview_url": "https://storage.googleapis.com/eleven-public-prod/premade/voices/CwhRBWXzGAHq8TQ4Fs17/12a2ba8b-4fb3-44b6-9bc7-da0afd076fc9.mp3"}, {"language": "es", "model_id": "eleven_multilingual_v2", "accent": "standard", "locale": null, "preview_url": "https://storage.googleapis.com/eleven-public-prod/premade/voices/CwhRBWXzGAHq8TQ4Fs17/f172f037-5e23-44ea-a08e-56ddb6447d5b.mp3"}], "safety_control": null, "voice_verification": {"requires_verification": false, "is_verified": false, "verification_failures": [], "verification_attempts_count": 0, "language": null, "verification_attempts": null}, "permission_on_resource": null, "is_owner": false, "is_legacy": true, "is_mixed": false, "created_at_unix": null}, {"voice_id": "D38z5RcWu1voky8WS1ja", "name": "Fin", "samples": null, "category": "premade", "fine_tuning": {"is_allowed_to_fine_tune": true, "state": {"eleven_flash_v2_5": "fine_tuned", "eleven_turbo_v2": "fine_tuned", "eleven_flash_v2": "fine_tuned", "eleven_v2_flash": "fine_tuned", "eleven_v2_5_flash": "fine_tuned"}, "verification_failures": [], "verification_attempts_count": 0, "manual_verification_requested": false, "language": "en", "progress": {"eleven_flash_v2_5": 1, "eleven_v2_flash": 1, "eleven_flash_v2": 1, "eleven_v2_5_flash": 1}, "message": {"eleven_multilingual_v2": "", "eleven_turbo_v2_5": "", "eleven_flash_v2_5": "Done!", "eleven_v2_flash": "Done!", "eleven_v2_5_flash": "Done!", "eleven_turbo_v2": "", "eleven_flash_v2": "Done!"}, "dataset_duration_seconds": null, "verification_attempts": null, "slice_ids": null, "manual_verification": null, "max_verification_attempts": 5, "next_max_verification_attempts_reset_unix_ms": 1700000000000}, "labels": {"accent": "irish", "description": "sailor", "age": "old", "gender": "male", "use_case": "characters"}, "description": null, "preview_url": "https://storage.googleapis.com/eleven-public-prod/premade/voices/D38z5RcWu1voky8WS1ja/a470ba64-1e72-46d9-ba9d-030c4155e2d2.mp3", "available_for_tiers": [], "settings": null, "sharing": null, "high_quality_base_model_ids": ["eleven_v2_flash", "eleven_flash_v2", "eleven_turbo_v2_5", "eleven_multilingual_v2", "eleven_multilingual_v1", "eleven_v2_5_flash", "eleven_flash_v2_5", "eleven_turbo_v2"], "verified_languages": [], "safety_control": null, "voice_verification": {"requires_verification": false, "is_verified": false, "verification_failures": [], "verification_attempts_count": 0, "language": null, "verification_attempts": null}, "permission_on_resource": null, "is_owner": false, "is_legacy": true, "is_mixed": false, "created_at_unix": null}, {"voice_id": "EXAVITQu4vr4xnSDxMaL", "name": "<PERSON>", "samples": null, "category": "premade", "fine_tuning": {"is_allowed_to_fine_tune": true, "state": {}, "verification_failures": [], "verification_attempts_count": 0, "manual_verification_requested": false, "language": "en", "progress": {}, "message": {}, "dataset_duration_seconds": null, "verification_attempts": null, "slice_ids": null, "manual_verification": null, "max_verification_attempts": 5, "next_max_verification_attempts_reset_unix_ms": 1700000000000}, "labels": {"accent": "american", "descriptive": "professional", "age": "young", "gender": "female", "language": "en", "use_case": "entertainment_tv"}, "description": "Young adult woman with a confident and warm, mature quality and a reassuring, professional tone.", "preview_url": "https://storage.googleapis.com/eleven-public-prod/premade/voices/EXAVITQu4vr4xnSDxMaL/01a3e33c-6e99-4ee7-8543-ff2216a32186.mp3", "available_for_tiers": [], "settings": null, "sharing": null, "high_quality_base_model_ids": ["eleven_turbo_v2", "eleven_multilingual_v2", "eleven_turbo_v2_5"], "verified_languages": [{"language": "en", "model_id": "eleven_turbo_v2", "accent": "american", "locale": "en-US", "preview_url": "https://storage.googleapis.com/eleven-public-prod/premade/voices/EXAVITQu4vr4xnSDxMaL/01a3e33c-6e99-4ee7-8543-ff2216a32186.mp3"}, {"language": "en", "model_id": "eleven_multilingual_v2", "accent": "american", "locale": "en-US", "preview_url": "https://storage.googleapis.com/eleven-public-prod/premade/voices/EXAVITQu4vr4xnSDxMaL/01a3e33c-6e99-4ee7-8543-ff2216a32186.mp3"}, {"language": "en", "model_id": "eleven_turbo_v2_5", "accent": "american", "locale": "en-US", "preview_url": "https://storage.googleapis.com/eleven-public-prod/premade/voices/EXAVITQu4vr4xnSDxMaL/01a3e33c-6e99-4ee7-8543-ff2216a32186.mp3"}, {"language": "fr", "model_id": "eleven_multilingual_v2", "accent": "standard", "locale": "fr-FR", "preview_url": "https://storage.googleapis.com/eleven-public-prod/premade/voices/EXAVITQu4vr4xnSDxMaL/093154f2-dd9f-4a4c-b5c3-81836c7ac3f6.mp3"}, {"language": "ar", "model_id": "eleven_multilingual_v2", "accent": "standard", "locale": null, "preview_url": "https://storage.googleapis.com/eleven-public-prod/premade/voices/EXAVITQu4vr4xnSDxMaL/ecb43dfe-b9f3-4691-8ee8-90c5ad3f4dbb.mp3"}, {"language": "zh", "model_id": "eleven_multilingual_v2", "accent": "standard", "locale": "cmn-CN", "preview_url": "https://storage.googleapis.com/eleven-public-prod/premade/voices/EXAVITQu4vr4xnSDxMaL/d9b9f54d-c08b-426e-80f3-b4f2089c3a59.mp3"}, {"language": "es", "model_id": "eleven_multilingual_v2", "accent": "standard", "locale": null, "preview_url": "https://storage.googleapis.com/eleven-public-prod/premade/voices/EXAVITQu4vr4xnSDxMaL/57ab5344-ed96-46fc-b319-82dc1c89bf66.mp3"}, {"language": "hi", "model_id": "eleven_multilingual_v2", "accent": "standard", "locale": "hi-IN", "preview_url": "https://storage.googleapis.com/eleven-public-prod/premade/voices/EXAVITQu4vr4xnSDxMaL/2f2caaae-ad5e-4ff2-a084-7a6067913a69.mp3"}], "safety_control": null, "voice_verification": {"requires_verification": false, "is_verified": false, "verification_failures": [], "verification_attempts_count": 0, "language": null, "verification_attempts": null}, "permission_on_resource": null, "is_owner": false, "is_legacy": false, "is_mixed": false, "created_at_unix": null}], "has_more": true, "total_count": 55, "next_page_token": "RVhBVklUUXU0dnI0eG5TRHhNYUx8"}